import os
import google.auth
from dotenv import load_dotenv

load_dotenv()  # for loading config from local .env


class Config:
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL',
                                        'postgresql+psycopg2://hull_np_rw:mWdsSrJDcYxNHx5@localhost:6543/platform_ai_service')
    TRINO_HOST = os.getenv('TRINO_HOST', '*************')
    TRINO_PORT = os.getenv('TRINO_PORT', 8080)
    TIMEOUT = 45
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    TESTING = False
    MAX_WORKERS = 5
    OPEN_AI_EMBEDDINGS = "text-embedding-3-small"

    # GCP Auth
    GCP_PROJECT_REGION = os.environ.get("GCP_PROJECT_REGION")
    GCP_CREDS, GCP_PROJECT_ID = google.auth.default()
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')

    OPENAI_ASSISTANT_ID = os.getenv('OPENAI_ASSISTANT_ID')
    OPENAI_ASSISTANT_THREAD_ID = os.getenv('OPENAI_ASSISTANT_THREAD_ID')


    #LLM TO USE
    LLM_IN_USE = os.environ.get("LLM_IN_USE","GEMINIAI_gemini-2.0-flash-001")

    # Keep this true for sending event data to Output stream response and set false for console logging
    SEND_EVENT_DATA_TO_OUTPUT_STREAM = os.getenv('SEND_EVENT_DATA_TO_OUTPUT_STREAM', "True").lower() == "true"
